webix.protoUI({
    name: "KEEDITOR",
    $init: function (config) {
        this.editorId = config.editorId;
        let z = config.editorId.split("-");
        let width = config.width ? config.width + "px" : "100%";
        let editor = "<textarea id='" + this.editorId + "' name='" + this.editorId + "' style='height:" + (config.editorHeight || 500) + "px;width:" + width + "'>" + config.value + "</textarea>";
        this.$view.innerHTML = editor;
        this.params = {
            uploadJson: "editorUploadserver.php?t=ke&id=" + z[0] + "&rid=" + z[1] + "&col=" + z[2] + "&master=" + config.editorMaster + "&key=" + config.editorKey,
            allowFileManager: true,
            afterCreate: function () {
                this.htmlTags['style'] = [];
                if (config.readonly) {
                    var doc = this.edit.doc;
                    doc.designMode = 'off';
                    doc.body.contentEditable = false;
                }
            }
        };
        if (config.readonly) {
            Object.assign(this.params, {
                readonly: true, cssData: "body{background:#fafafa}", items: ["print", "|", "fullscreen"]
            });
        }
        this.$ready.push(this._require_editor);
    },
    _require_editor: function () {
        let that = this;
        webix.require(["editor/kindeditor.js"])
            .then(function () {
                if (document.getElementById(that.editorId)) {
                    that._editor = KindEditor.create('textarea[name="' + that.editorId + '"]', that.params);
                    that._editor.toolbar.disableAll(false, []);
                }
            })
            .catch(function (e) {
                console.log(e);
            })
    },
    refresh: function () {
        if (this._editor) {
            KindEditor.remove('textarea[name="' + this.editorId + '"]');
        }
        this._editor = KindEditor.create('textarea[name="' + this.editorId + '"]', this.params);
        this._editor.toolbar.disableAll(false, []);
        this._editor.html(this.config.value);
    },
    setValue: function (value) {
        this.refresh()
        this.config.value = value;
        this._editor.html(value);
    },
    getValue: function (value) {
        return this._editor ? this._editor.html() : this.config.value;
    }
}, webix.ui.view);

webix.protoUI({
    name: "FREDITOR",
    $init: function (config) {
        this.editorId = config.editorId;
        var z = config.editorId.split("-");//这里不能用^符号，^s是特殊字符，控件通过Document.querySelectorAll定位不了div
        this.params = {
            language: 'zh_cn',
            fullPage: true,
            heightMax: 300,
            heightMin: 300,
            fileUploadURL: "editorUploadserver.php?t=fr",
            fileUploadParam: "upload",
            fileUploadParams: {
                id: z[0],
                rid: z[1],
                col: z[2],
                master: config.editorMaster,
                key: config.editorKey
            },
            imageUploadURL: "editorUploadserver.php?t=fr",
            imageUploadParam: "upload",
            htmlRemoveTags: ["script"],
            imageUploadParams: {
                id: config.editorId.split("-")[0],
                rid: z[1],
                col: z[2],
                master: config.editorMaster,
                key: config.editorKey
            },
            events: {
                'image.error': function (error, response) {
                    webix.message(response, "error", 5000);
                },
                'file.error': function (error, response) {
                    webix.message(response, "error", 5000);
                }
            }
        };
        if (config.readonly) {
            this.params.events.initialized = function () {
                this.$el.attr('contenteditable', false);
                const newEl = this.$el[0].cloneNode(true);
                this.$el[0].parentNode.replaceChild(newEl, this.$el[0]);
                const resizeObserver = new ResizeObserver(entries => {
                    for (let entry of entries) {
                        const { height } = entry.target.getBoundingClientRect();
                        newEl.ownerDocument.defaultView.frameElement.style.height = height + 'px';
                    }
                });
                resizeObserver.observe(newEl);
            }
            this.params.events['click'] = function (clickEvent) {
                setTimeout(this.popups.hideAll(), 500);
            }
            Object.assign(this.params, { toolbarButtons: ["myprint", "fullscreen"] });
        } else {
            Object.assign(this.params, { toolbarButtons: this._toolbarButtons });
        }
        /*
        {
            'moreText': {
                'buttons': ['bold', 'italic', 'underline', 'strikeThrough', 'subscript', 'superscript', 'fontFamily', 'fontSize', 'textColor', 'backgroundColor', 'inlineClass', 'inlineStyle', 'clearFormatting']
            },
            'moreParagraph': {
                'buttons': ['alignLeft', 'alignCenter', 'formatOLSimple', 'alignRight', 'alignJustify', 'formatOL', 'formatUL', 'paragraphFormat', 'paragraphStyle', 'lineHeight', 'outdent', 'indent', 'quote']
            },
            'moreRich': {
                'buttons': ['insertLink', 'insertImage', 'insertVideo', 'insertTable', 'emoticons', 'fontAwesome', 'specialCharacters', 'embedly', 'insertFile', 'insertHR']
            },
            'moreMisc': {
                'buttons': ['undo', 'redo', 'fullscreen', 'print', 'getPDF', 'spellChecker', 'selectAll', 'html', 'help'],
                'align': 'right',
                'buttonsVisible': 2
            }
        }
        */
        if (config.editorHeight) {
            this.params.heightMax = config.editorHeight;
            this.params.heightMin = config.editorHeight;
        }
        var editor = "<div id='" + this.editorId + "' style='overflow-y:auto'>" + config.value + "</div>";
        this.$view.innerHTML = editor;
        this.$ready.push(this._require_editor);
    },
    _toolbarButtons: [
        [
            'html',
            'print',
            'fullscreen',
            'undo',
            'redo'
        ],
        [
            'bold',
            'italic',
            'underline',
            'paragraphFormat',
            'fontSize',
            'textColor',
            'backgroundColor',
            'align',
            'formatOL',
            'formatUL',
            'insertLink',
            'insertFile',
            'insertImage',
            'insertTable',
            'clearFormatting']
    ],
    _require_editor: function () {
        webix.require(["js/froala/froala_editor.pkgd.min.js", "js/froala/froala_editor.pkgd.min.css", "js/froala/froala_style.min.css", "js/froala/zh_cn.js"])
            .then(webix.bind(this._render_editor, this))
            .catch(function (e) {
                console.log(e);
            });
    },
    _render_editor: function () {
        FroalaEditor.DefineIcon('myprint', { NAME: 'print', SVG_KEY: 'print' });//扩展按钮方法
        FroalaEditor.RegisterCommand('myprint', {
            title: '打印',
            focus: true,
            undo: true,
            refreshAfterCallback: true,
            callback: function () {
                if ($$(this.$box[0].getAttribute('id')).config.readonly) {
                    this.html.set("<div id='" + this.$box[0].getAttribute('id') + "' style='overflow-y:auto'>" + $$(this.$box[0].getAttribute('id')).config.value + "</div>");
                }
                setTimeout(() => this.print.run(), 500);
            }
        });
        this._editor = new FroalaEditor("#" + this.editorId, this.params);
    },
    refresh: function () {
        if (this._editor.html) {
            this._editor.destroy();
        }
        this.$view.innerHTML = "<div id='" + this.editorId + "' style='overflow-y:auto'>" + this.config.value + "</div>";
        this._render_editor();
    },
    setValue: function (value) {
        this.config.value = value;
        this._editor.html.insert(value);
    },
    getValue: function (value) {
        return this._editor ? (this._editor.html ? this._editor.html.get(true) : this.config.value) : this.config.value;
    }
}, webix.ui.view);

webix.protoUI({
    name: "echarts",
    $init: function (config) {
        this.$view.innerHTML = "<div id='echarts_" + config.id + "' style='height:100%;width:100%'></div>";
        this.$ready.push(this._require_echarts);
    },

    _require_echarts: function () {
        webix.require(["js/echarts.min.js", "js/theme.js"])
            .then(webix.bind(this.showChart, this))
            .catch(function (e) {
                console.log(e);
            });
    },

    showChart: function () {
        var chart = echarts.init(document.getElementById("echarts_" + this.config.id), this.config.theme || null);
        chart.setOption(this.config.data);
        window.onresize = function () {
            chart.resize();
        }
    }

}, webix.ui.view);
webix.protoUI({
    name: "vue",
    $init: function (config) {
        config.id = config.id.replace('$', '')
        this.$view.innerHTML = "<div id='vue_" + config.id + "' style='height:100%;width:100%'></div>";
        this.$ready.push(this._require_vue);
    },

    _require_vue: function () {
        webix.require(["js/vue.min.js"])
            .then(webix.bind(this.loadVue, this))
            .catch(function (e) {
                console.log(e);
            });
    },
    loadVue: function () {
        // 1. 提取 template
        let componentText = this.config.content
        let mountId = 'vue_' + this.config.id
        const templateMatch = componentText.match(/<template>([\s\S]*?)<\/template>/);
        const template = templateMatch ? templateMatch[1].trim() : null;

        // 2. 提取 script
        const scriptMatch = componentText.match(/<script>([\s\S]*?)<\/script>/);
        let scriptContent = scriptMatch ? scriptMatch[1].trim() : null;

        // 3. 提取 style
        const styleMatch = componentText.match(/<style[^>]*>([\s\S]*?)<\/style>/);
        const styleContent = styleMatch ? styleMatch[1].trim() : null;

        if (!template || !scriptContent) {
            console.error("组件必须包含 <template> 和 <script>");
            return;
        }

        // 4. 注入 style 到页面（只注入一次）
        if (styleContent) {
            const styleId = `__injected-style-${mountId}`;
            if (!document.getElementById(styleId)) {
                const styleEl = document.createElement("style");
                styleEl.id = styleId;
                styleEl.innerHTML = styleContent;
                document.head.appendChild(styleEl);
            }
        }

        // 5. 编译 template
        const compiled = Vue.compile(template);

        scriptContent = scriptContent.replace(/export\s+default/, "return");

        // 7. 执行 scriptContent
        const componentDef = new Function(scriptContent)();
        // 保留原有props并添加rid
        componentDef.props = Object.assign(componentDef.props || {}, {
            rid: {
                type: String,
                default: this.config.rid || null
            },
            pid: {
                type: String,
                default: this.config.pid || null
            },
            fromDiy: {
                type: Boolean,
                default: this.config.fromDiy || false
            }
        });

        // 8. 合并 render 函数
        const finalComponent = Object.assign({}, componentDef, {
            render: compiled.render,
            staticRenderFns: compiled.staticRenderFns
        });

        // 9. 挂载到页面
        new Vue({
            render: h => h(finalComponent)
        }).$mount(`#${mountId}`);

    }
}, webix.ui.view);
webix.protoUI({
    name: "MDPREVIEW",
    $init: function (config) {
        this.config = config;
        this.$ready.push(this._require_md);
        try {
            this.bind('onViewShow', function () {
                var nowWindow = window;
                while (nowWindow.parent != nowWindow) {
                    nowWindow = nowWindow.parent;
                }
                nowWindow.mermaid.run({
                    querySelector: '.mermaid',
                })
            });
        } catch (e) { }
        this.attachEvent('onViewShow', function () {
            var nowWindow = window;
            while (nowWindow.parent != nowWindow) {
                nowWindow = nowWindow.parent;
            }
            if (nowWindow.mermaid) {
                nowWindow.mermaid.run({
                    querySelector: '.mermaid',
                })
            }
        });
    },
    _require_md: function () {
        webix.require(["js/extras/index.css", "js/extras/mdbr.js", "js/extras/mermaid.min.js"])
            .then(() => {
                webix.bind(this.showMD, this);
                var nowWindow = window;
                var nextWindow = window.parent;
                while (nowWindow != nextWindow) {
                    nowWindow = nextWindow;
                    nextWindow = nowWindow.parent;
                }
                nowWindow.mermaid = mermaid;
                this.showMD();
            })
            .catch(function (e) {
                console.log(e);
            })
    },
    showMD: function (that) {
        this.$view.innerHTML = "<markdown id='md_" + this.config.data[1] + "' style='display:block;height:calc(" + (this.config.height || 300) + "px - 2em)' class='markdown'>" + window.md.render(this.config.data[0].replace(/&gt;/g, ">")) + "</markdown>"
        var nowWindow = window;
        while (nowWindow.parent != nowWindow) {
            nowWindow = nowWindow.parent;
        }
        nowWindow.mermaid.run({
            querySelector: '.mermaid',
        });
    },
    refresh: function () {
        this.showMD();
    }
}, webix.ui.view);
webix.protoUI({
    name: 'multibatchdatatable',

    /**
     * 例子['8','1'],'',null,false,true,true
     * @param batch 类别 ’1‘ 或类别数组 ['8','1']
     * @param oldBatch '' 用于用户自定义操作
     * @param colFunc 用户函数，会调用colFunc(this,batch,oldBatch)
     * @param hideOthers 是否根据batch隐藏其他非batch列
     * @param withSpans 是否显示、隐藏列时，联动span内的列
     * @param every 与batch的关系，true表示必须全包含batch，false表示只需包含batch中的一个
     */
    showColumnBatch: function (batch, oldBatch, colFunc = null, hideOthers = true, withSpans = false, every = false) {
        var mode = false;
        var spans = withSpans ? { spans: true } : {};
        var cols = this.getColumns(true);
        for (var col of cols) {
            if (col.batch) {
                var batches = [col.batch];
                if (typeof (col.batch) === "string") {
                    batches = col.batch.split(",");
                }
                if (col.batch instanceof Array) {
                    batches = col.batch;
                }
                var targetBatch = [batch];
                if (batch instanceof Array) {
                    targetBatch = batch
                }
                targetBatch.forEach(item => item.toString());
                if (every) {
                    batchFlag = targetBatch.every(item => batches.includes(item));
                } else {
                    batchFlag = targetBatch.some(item => batches.includes(item));
                }
                var visible = this.isColumnVisible(col.id);
                if (batchFlag && !visible) {
                    this.showColumn(col.id, spans, mode);
                } else if (hideOthers && !batchFlag && visible) {
                    this.hideColumn(col.id, spans, mode, true);
                }
            }
        }
        if (colFunc != null) {
            colFunc(this, batch, oldBatch);
        }
    },
}, webix.ui.datatable);
webix.protoUI({
    name: "rate",
    $init: function (config) {
        let labes = ''
        for (let i = 5; i > 0; i--) {
            labes += `<input type="radio" name="${config.name}" id="${config.name + i}" value="${i}"/><label for="${config.name + i}"><i class="mdi mdi-star-outline"></i></label>`
        }
        this.$view.innerHTML = `<div class="rating">${labes}</div>`;
        let val = parseInt(config.value)
        if (isNaN(val)) {
            val = 0
        }
        if (val > 5) {
            val = 5
        }
        for (let input of this.$view.getElementsByTagName('input')) {
            if (input.value == val) {
                input.checked = true
            }
            if (config.readonly) {
                input.disabled = true
            }
        }
    },
    setValue: function (value) {
        let val = parseInt(value)
        if (isNaN(val)) {
            val = 0
        }
        if (val > 5) {
            val = 5
        }
        for (let input of this.$view.getElementsByTagName('input')) {
            if (input.value == val) {
                input.checked = true
            }
        }
    },
    getValue: function () {
        for (let input of this.$view.getElementsByTagName('input')) {
            if (input.checked) {
                return parseInt(input.value)
            }
        }
        return 0
    }
}, webix.ui.view);
webix.protoUI({
    name: "dotMap",
    $init: function (config) {
        this.$view.innerHTML = "<div id='dotMap_" + config.name + "' style='position:relative;height:" + (config.height || 300) + "px;width:100%'><div id='search_btn_" + config.name + "' class='map-search-btn'><i class='mdi mdi-magnify'></i> 搜索</div></div>";
        // Add CSS for search button positioning
        let style = document.createElement('style');
        style.textContent = `
            .map-search-btn {
                position: absolute;
                width: 70px;
                top: 10px;
                left: 30px;
                z-index: 1;
                background-color: white;
                border-radius: 4px;
                padding: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                cursor: pointer;
            }
            .map-search-btn:hover {
                background-color: #f0f0f0;
            }
        `;
        document.head.appendChild(style);

        this.$ready.push(this._require);
    },
    _require: function () {
        this.type = this.config.type
        webix.require(["js/map/ol.js", "js/map/ol.css"])
            .then(webix.bind(this.initMap, this))
            .catch(function (e) {
                console.log(e);
            });
    },
    showDot(data) {
        let coordinates = this._transform.gcj02_to_wgs84(parseFloat(data.lng), parseFloat(data.lat));
        // 如果配置了半径，则绘制圆形区域
        if (this.config.radius && !isNaN(parseFloat(this.config.radius)) && parseFloat(this.config.radius) > 0) {
            var radius = parseFloat(this.config.radius);

            // 坐标从 EPSG:4326 转换到 EPSG:3857
            var center_3857 = ol.proj.transform(coordinates, 'EPSG:4326', 'EPSG:3857');

            // 在 EPSG:3857 下创建圆形（半径单位为米）
            var circle_geom_3857 = new ol.geom.Circle(center_3857, radius);

            // 从圆形几何体创建多边形
            var circle_poly_3857 = ol.geom.Polygon.fromCircle(circle_geom_3857, 64);

            // 将多边形转换回 EPSG:4326
            var circle_poly_4326 = circle_poly_3857.clone().transform('EPSG:3857', 'EPSG:4326');

            // 创建圆形要素
            var circleFeature = new ol.Feature(circle_poly_4326);

            // 定义圆形样式
            var circleStyle = new ol.style.Style({
                stroke: new ol.style.Stroke({
                    color: '#C4E1F94D',
                    width: 2
                }),
                fill: new ol.style.Fill({
                    color: '#63B8FF64'
                })
            });
            circleFeature.setStyle(circleStyle);

            // 将圆形要素添加到矢量源
            this.vectorSource.addFeature(circleFeature);
        }

        var iconFeature = new ol.Feature({
            geometry: new ol.geom.Point(coordinates),
            data: data // 存储点位的数据，包括地址信息和ID
        });
        let icon = 'js/map/location_l.png'
        if(data.onsite){
            icon = 'js/map/location_s.png'
        }
        // 定义图标样式
        var iconStyle = new ol.style.Style({
            image: new ol.style.Icon({
                anchor: [0.5, 1],  // 图标的锚点
                src: icon,  // 图标 URL
                scale: 0.5  // 根据需要调整图标大小
            })
        });

        // 将样式应用到 feature 上
        iconFeature.setStyle(iconStyle);

        // 将 feature 添加到矢量源中
        this.vectorSource.addFeature(iconFeature);
        
        // 确保我们有点击交互功能
        this._ensureClickInteraction();
    },
    
    // 添加确保点击交互存在的方法
    _ensureClickInteraction() {
        if (!this._clickInteractionAdded) {
            // 创建弹出框元素
            if (!this._popupContainer) {
                this._popupContainer = document.createElement('div');
                this._popupContainer.className = 'ol-popup';
                this._popupContainer.style.position = 'absolute';
                this._popupContainer.style.backgroundColor = 'white';
                this._popupContainer.style.padding = '15px';
                this._popupContainer.style.borderRadius = '5px';
                this._popupContainer.style.border = '1px solid #ccc';
                this._popupContainer.style.boxShadow = '0 1px 4px rgba(0,0,0,0.2)';
                this._popupContainer.style.minWidth = '200px';
                this._popupContainer.style.zIndex = '1000';
                

                
                // 创建内容容器
                this._popupContent = document.createElement('div');
                this._popupContainer.appendChild(this._popupContent);
                
                // 创建弹出框 Overlay
                this._popup = new ol.Overlay({
                    element: this._popupContainer,
                    positioning: 'bottom-center',
                    stopEvent: true,
                    offset: [0, -10]
                });
                this.map.addOverlay(this._popup);
            }
            
            // 添加点击事件
            this.map.on('click', (evt) => {
                // 隐藏任何已经显示的弹出框
                this._popup.setPosition(undefined);
                
                // 判断是否点击了要素
                const feature = this.map.forEachFeatureAtPixel(evt.pixel, (feature) => feature);
                
                if (feature && feature.getGeometry().getType() === 'Point') {
                    // 获取点位数据
                    const data = feature.get('data');
                    if (data) {
                        // 设置弹出框内容
                        this._popupContent.innerHTML = '';
                        
                        // 添加地址信息
                        const addrDiv = document.createElement('div');
                        addrDiv.style.marginBottom = '10px';
                        addrDiv.innerHTML = '<strong>地址:</strong> ' + (data.addr || '未知地址');
                        this._popupContent.appendChild(addrDiv);
                        
                        // 添加删除按钮
                        const deleteBtn = document.createElement('button');
                        deleteBtn.innerHTML = '删除';
                        deleteBtn.style.backgroundColor = '#ff4d4d';
                        deleteBtn.style.color = 'white';
                        deleteBtn.style.border = 'none';
                        deleteBtn.style.padding = '5px 10px';
                        deleteBtn.style.borderRadius = '3px';
                        deleteBtn.style.cursor = 'pointer';
                        deleteBtn.onclick = () => {
                            // 从地图中删除该点
                            
                            // 从数据库中删除
                            if (data.rid) {
                                xAjax({
                                    url: "dotmap.php",
                                    data: {
                                        action: "delete",
                                        rid: data.rid,
                                        edit_rid: this.editData.edit_rid,
                                        edit_taskid: this.editData.edit_taskid,
                                        edit_nodeid: this.editData.edit_nodeid,
                                        edit_master: this.editData.edit_master,
                                        edit_key: this.editData.edit_key,
                                        colname: this.config.name,
                                        id: this.config.form
                                    }
                                }, (res) => {
                                    webix.message({type: "success", text: "删除成功"});
                                    // 隐藏弹出框
                                    this.vectorSource.removeFeature(feature);
                                    this._popup.setPosition(undefined);
                                });
                            } else {
                                // 如果没有rid，只是移除显示的点
                                this._popup.setPosition(undefined);
                            }
                        };
                        if(!this.config.disabled){
                            this._popupContent.appendChild(deleteBtn);
                        }
                        
                        // 设置弹出框位置
                        const coordinates = feature.getGeometry().getCoordinates();
                        this._popup.setPosition(coordinates);
                    }
                }
            });
            
            this._clickInteractionAdded = true;
        }
    },
    setDot(coordinates) {
        if (!coordinates || coordinates.length !== 2 || isNaN(coordinates[0]) || isNaN(coordinates[1])||this.config.onsite) {
            return; // 忽略无效坐标
        }

        // 清除之前的临时按钮（如果存在）
        if (this.confirmOverlay) {
            this.map.removeOverlay(this.confirmOverlay);
            this.confirmOverlay = null;
        }

        // 创建确认按钮元素
        const button = document.createElement('button');
        button.innerText = '确认打点';
        button.style.backgroundColor = '#007bff';
        button.style.color = 'white';
        button.style.border = 'none';
        button.style.padding = '5px 10px';
        button.style.borderRadius = '4px';
        button.style.cursor = 'pointer';
        button.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3)';
        button.addEventListener('click',(e)=>{
            // 转换为GCJ02坐标
            const coords_gcj = this._transform.wgs84_to_gcj02(coordinates[0], coordinates[1]);

            // 创建新数据项（初始addr为空）
            const newData = {
                lng: coords_gcj[0],
                lat: coords_gcj[1],
                addr: '' // 默认地址为空
            };

            // 使用天地图逆地理编码API查询地址
            const searchUrl = "https://api.tianditu.gov.cn/geocoder";
            const params = {
                type: "geocode",
                tk: this.config.key || 'fbadfac86e6fd50f4e502422a5606c2a',
                postStr: JSON.stringify({
                    lon: coords_gcj[0],
                    lat: coords_gcj[1],
                    ver: 1
                })
            };
            webix.ajax().get(searchUrl, params, (text, data) => {
                const jsonData = data.json();
                if (jsonData && jsonData.result && jsonData.result.formatted_address) {
                    newData.addr = jsonData.result.formatted_address; // 设置查询到的地址
                } else {
                    newData.addr = '未知地址'; // 查询失败时的默认值
                }
            }).finally(()=>{
                if(!this.config.multi){
                    this.vectorSource.clear();
                }
                xAjax({
                    url: "dotmap.php",
                    data: {
                        longitude: newData.lng,
                        latitude: newData.lat,
                        address: newData.addr,
                        edit_rid: this.editData.edit_rid,
                        edit_taskid: this.editData.edit_taskid,
                        edit_nodeid: this.editData.edit_nodeid,
                        edit_master: this.editData.edit_master,
                        edit_key: this.editData.edit_key,
                        colname: this.config.name,
                        id: this.config.form
                    },
                },(res)=>{
                    newData.rid = res.data;
                    this.showDot(newData);
                })
                if (this.confirmOverlay) {
                    this.map.removeOverlay(this.confirmOverlay);
                    this.confirmOverlay = null;
                }
            });
        });

        // 创建Overlay并添加到地图
        this.confirmOverlay = new ol.Overlay({
            element: button,
            position: coordinates,
            positioning: 'bottom-center', // 按钮定位在坐标下方居中
            stopEvent: false // 允许事件冒泡
        });
        this.map.addOverlay(this.confirmOverlay);
    },
    _transform: {
        // a: 6378245.0, 1/f = 298.3
        // ee = (a^2 - b^2) / a^2
        a: 6378245.0,
        ee: 0.006693421622965943,

        // wgs84 to gcj02
        wgs84_to_gcj02: function (lng, lat) {
            if (this.out_of_china(lng, lat)) {
                return [lng, lat]
            }
            var dlat = this.transform_lat(lng - 105.0, lat - 35.0);
            var dlng = this.transform_lng(lng - 105.0, lat - 35.0);
            var radlat = lat / 180.0 * Math.PI;
            var magic = Math.sin(radlat);
            magic = 1 - this.ee * magic * magic;
            var sqrtmagic = Math.sqrt(magic);
            dlat = (dlat * 180.0) / ((this.a * (1 - this.ee)) / (magic * sqrtmagic) * Math.PI);
            dlng = (dlng * 180.0) / (this.a / sqrtmagic * Math.cos(radlat) * Math.PI);
            var mglat = lat + dlat;
            var mglng = lng + dlng;
            return [mglng, mglat];
        },

        // gcj02 to wgs84
        gcj02_to_wgs84: function (lng, lat) {
            if (this.out_of_china(lng, lat)) {
                return [lng, lat]
            }
            var dlat = this.transform_lat(lng - 105.0, lat - 35.0);
            var dlng = this.transform_lng(lng - 105.0, lat - 35.0);
            var radlat = lat / 180.0 * Math.PI;
            var magic = Math.sin(radlat);
            magic = 1 - this.ee * magic * magic;
            var sqrtmagic = Math.sqrt(magic);
            dlat = (dlat * 180.0) / ((this.a * (1 - this.ee)) / (magic * sqrtmagic) * Math.PI);
            dlng = (dlng * 180.0) / (this.a / sqrtmagic * Math.cos(radlat) * Math.PI);
            var mglat = lat + dlat;
            var mglng = lng + dlng;
            return [lng * 2 - mglng, lat * 2 - mglat]
        },
        transform_lat: function (lng, lat) {
            var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
            ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
            ret += (20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin(lat / 3.0 * Math.PI)) * 2.0 / 3.0;
            ret += (160.0 * Math.sin(lat / 12.0 * Math.PI) + 320 * Math.sin(lat * Math.PI / 30.0)) * 2.0 / 3.0;
            return ret
        },

        transform_lng: function (lng, lat) {
            var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
            ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
            ret += (20.0 * Math.sin(lng * Math.PI) + 40.0 * Math.sin(lng / 3.0 * Math.PI)) * 2.0 / 3.0;
            ret += (150.0 * Math.sin(lng / 12.0 * Math.PI) + 300.0 * Math.sin(lng / 30.0 * Math.PI)) * 2.0 / 3.0;
            return ret
        },

        out_of_china: function (lng, lat) {
            return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55)
        }
    },
    initMap() {
        let that = this
        this.editData = $$("edit^" + this.config.form).config.cfg;
        let key = this.config.key || 'fbadfac86e6fd50f4e502422a5606c2a';
        let center;
        if (this.config.data[0] && this.config.data[0].lng && this.config.data[0].lat) {
            const coords_gcj = [this.config.data[0].lng,this.config.data[0].lat]
            center = this._transform.gcj02_to_wgs84(parseFloat(coords_gcj[0]), parseFloat(coords_gcj[1]));
        } else if (localStorage.getItem('mapCenter')) {
            const coords_gcj = localStorage.getItem('mapCenter').split(',');
            center = this._transform.gcj02_to_wgs84(parseFloat(coords_gcj[0]), parseFloat(coords_gcj[1]));
        } else if (this.config.center) {
            center = this._transform.gcj02_to_wgs84(parseFloat(this.config.center[0]), parseFloat(this.config.center[1]));
        }
        let zoom = this.config.zoom || 12;
        // 天地图图层
        var TiandiMap_vec = new ol.layer.Tile({
            name: "天地图矢量图层",
            source: new ol.source.XYZ({
                url: "https://t0.tianditu.gov.cn/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=" + key,
                wrapX: false
            }),
            zIndex: 2,
            opacity: 1,
            visible: true
        });
        var TiandiMap_cva = new ol.layer.Tile({
            name: "天地图矢量注记图层",
            source: new ol.source.XYZ({
                url: "https://t0.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=" + key,
                wrapX: false
            }),
            zIndex: 3,
            opacity: 1,
            visible: true
        });

        // 矢量图层和源
        this.vectorSource = new ol.source.Vector({
            features: [],  // 初始为空
            visible: true
        });
        let vectorLayer = new ol.layer.Vector({
            source: that.vectorSource,
            zIndex: 999
        });

        let view = new ol.View({
            center: center,
            zoom: zoom,
            maxZoom: this.config.max_zoom || 18,
            projection: 'EPSG:4326'
        });

        let layers = [vectorLayer, TiandiMap_vec, TiandiMap_cva];  // 确保矢量层包含在 layers 中

        // 创建地图实例
        this.map = new ol.Map({
            layers: layers,
            target: 'dotMap_' + this.config.name,
            view: view
        });
        function search(specify="156000000"){
            let searchText = $$("search_input_" + that.config.name).getValue();
            if(!searchText){
                return;
            }
            let searchUrl = "https://api.tianditu.gov.cn/v2/search";
            let params = {
                type: "query",
                wd: searchText,
                tk: that.config.key || 'fbadfac86e6fd50f4e502422a5606c2a',
                postStr: { "keyWord": searchText, "queryType": 12, "start": 0, "count": 20, "specify": specify },
                responseData: "json"
            };
            webix.ajax().get(searchUrl, params, function (text, data) {
                let jsonData = data.json();

                // 检查是否返回城市列表（多城市情况）
                if (jsonData && jsonData.statistics && jsonData.statistics.allAdmins && jsonData.statistics.allAdmins.length > 0) {
                    // 显示城市选择列表
                    $$("search_results_" + that.config.name).clearAll();
                    $$("search_results_" + that.config.name).define("template", (o)=>{
                        return "<div><b>"+o.adminName+"</b>(包含 "+o.count+" 个结果)</div>";
                    });
                    $$("search_results_" + that.config.name).define("citySelection", true);
                    $$("search_results_" + that.config.name).parse(jsonData.statistics.allAdmins);
                    $$("search_results_" + that.config.name).refresh();
                }
                // 正常POI结果
                else if (jsonData && jsonData.pois && jsonData.pois.length > 0) {
                    // 将搜索结果加载到列表中
                    $$("search_results_" + that.config.name).clearAll();
                    // 确保模板是POI模板
                    $$("search_results_" + that.config.name).define("template", (o)=>{
                        return "<div><b>"+o.name+"</b>-"+o.address+" </div>";
                    });
                    $$("search_results_" + that.config.name).define("citySelection", false);
                    $$("search_results_" + that.config.name).parse(jsonData.pois);
                    $$("search_results_" + that.config.name).refresh();
                } else {
                    $$("search_results_" + that.config.name).clearAll();
                    webix.message("未找到匹配的位置", "error");
                }
            }).fail(function () {
                webix.message("搜索请求失败", "error");
            });
        }
        // Add search button click handler
        document.getElementById('search_btn_' + this.config.name).addEventListener('click', function () {
            if($$("search_window_" + that.config.name)){
                $$("search_window_" + that.config.name).show();
                return;
            }
            // 创建搜索对话框
            webix.ui({
                view: "window",
                id: "search_window_" + that.config.name,
                width: 600,
                height: 400,
                position: "center",
                modal: true,
                head: {
                    view: "toolbar", cols: [
                        { view: "label", label: "搜索位置" },
                        { view: "icon", icon: "wxi-close", click: function () { $$("search_window_" + that.config.name).hide(); } }
                    ]
                },
                body: {
                    rows: [
                        {
                            view: "form",
                            id: "search_form_" + that.config.name,
                            elements: [
                                {
                                    cols: [
                                        { view: "text", id: "search_input_" + that.config.name, placeholder: "输入地点名称" },
                                        {
                                            view: "button",
                                            value: "搜索",
                                            width: 70,
                                            on: {
                                                onItemClick: function () {
                                                    search();
                                                }
                                            }
                                        }
                                    ]
                                },
                            ]
                        },
                        {
                            view: "list",
                            id: "search_results_" + that.config.name,
                            select: true,
                            data: [],
                            height: 300,
                            click: function (id) {
                                let item = this.getItem(id);

                                // 检查是否为城市选择模式
                                if (this.config.citySelection && item && item.adminCode) {
                                    // 使用选中的城市进行搜索
                                    search(item.adminCode);
                                    return;
                                }
                                // 普通POI点击处理
                                if (item && item.lonlat) {
                                    // 解析经纬度坐标
                                    let lonlat = item.lonlat.split(",");
                                    let coords_wgs = [parseFloat(lonlat[0]), parseFloat(lonlat[1])];

                                    // 设置地图中心到搜索结果
                                    that.map.getView().setCenter(coords_wgs);
                                    that.map.getView().setZoom(15);

                                    // 设置点标记
                                    that.setDot(coords_wgs);

                                    // 关闭搜索窗口
                                    $$("search_window_" + that.config.name).hide();
                                }
                            }
                        }
                    ]
                }
            }).show();
        });

        if (this.config.data) {
            for(let i=0;i<this.config.data.length;i++){
                this.showDot(this.config.data[i]);
            }
        }
        // 监听地图的点击事件
        if (!this.config.disabled&&!this.config.onsite) {
            this.map.on('click',  (event) =>{
                if (this.map.hasFeatureAtPixel(event.pixel)) {
                    return;
                }
                if (this.confirmOverlay) {
                    setTimeout(()=>{
                        this.map.removeOverlay(this.confirmOverlay);
                        this.confirmOverlay = null;
                    })
                    return
                }
                var coordinates = event.coordinate;  // 获取点击位置的坐标 (EPSG:3857)
                that.setDot(coordinates)
            });
        }
        this.$view.addEventListener('wheel', function (e) {
            e.stopPropagation();
            if (e.preventDefault) {
                e.preventDefault();
            }
        }, { passive: false });
    }
}, webix.ui.view)
