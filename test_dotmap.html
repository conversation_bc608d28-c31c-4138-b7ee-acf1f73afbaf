<!DOCTYPE html>
<html>
<head>
    <title>DotMap 测试</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-container {
            width: 800px;
            height: 600px;
            border: 1px solid #ccc;
            margin: 20px 0;
        }
        .description {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .fix-description {
            background-color: #f0fff0;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>DotMap 组件测试</h1>
    
    <div class="description">
        <h3>问题描述：</h3>
        <p>原来的 dotmap 组件存在一个问题：显示信息的弹窗和确认打点的按钮可能会同时出现，造成界面混乱。</p>
    </div>
    
    <div class="fix-description">
        <h3>修复内容：</h3>
        <ul>
            <li>在点击已有点位显示信息弹窗时，自动隐藏确认打点按钮</li>
            <li>在点击空白处显示确认打点按钮时，自动隐藏信息弹窗</li>
            <li>确保两个UI元素不会同时显示，提供更好的用户体验</li>
        </ul>
    </div>
    
    <h3>测试说明：</h3>
    <ol>
        <li>点击地图上的空白区域 → 应该显示"确认打点"按钮</li>
        <li>点击已有的点位 → 应该显示信息弹窗（包含地址和删除按钮）</li>
        <li>在显示弹窗时点击空白区域 → 弹窗应该消失，显示确认打点按钮</li>
        <li>在显示确认打点按钮时点击已有点位 → 按钮应该消失，显示信息弹窗</li>
    </ol>
    
    <div class="test-container" id="mapContainer">
        <!-- 这里应该放置 dotmap 组件 -->
        <p style="text-align: center; line-height: 600px; color: #666;">
            请在实际的 webix 环境中测试 dotmap 组件
        </p>
    </div>
    
    <h3>修改的关键代码位置：</h3>
    <ul>
        <li><strong>_ensureClickInteraction 方法</strong>：在显示信息弹窗前清除确认打点按钮</li>
        <li><strong>setDot 方法</strong>：在显示确认打点按钮前隐藏信息弹窗</li>
        <li><strong>地图点击事件</strong>：优化了点击逻辑，避免冲突</li>
    </ul>
</body>
</html>
